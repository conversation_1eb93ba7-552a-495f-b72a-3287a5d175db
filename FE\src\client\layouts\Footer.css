.footer {
  background-color: #2c3e50;
  color: #ecf0f1;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #3498db;
}

.footer-subtitle {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ecf0f1;
}

.footer-description {
  line-height: 1.6;
  margin-bottom: 20px;
  color: #bdc3c7;
}

.social-links {
  display: flex;
  gap: 10px;
}

.social-link {
  display: inline-block;
  width: 40px;
  height: 40px;
  background-color: #34495e;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  font-size: 18px;
  text-decoration: none;
  transition: background-color 0.3s;
}

.social-link:hover {
  background-color: #3498db;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-link {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-link:hover {
  color: #3498db;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #bdc3c7;
}

.contact-icon {
  font-size: 16px;
  width: 20px;
}

/* Newsletter Section */
.newsletter-section {
  background-color: #34495e;
  padding: 30px 0;
}

.newsletter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.newsletter-section h4 {
  font-size: 20px;
  margin-bottom: 10px;
  color: #ecf0f1;
}

.newsletter-section p {
  color: #bdc3c7;
  margin-bottom: 20px;
}

.newsletter-form {
  display: flex;
  justify-content: center;
  gap: 10px;
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
}

.newsletter-btn {
  padding: 12px 20px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.newsletter-btn:hover {
  background-color: #2980b9;
}

/* Footer Bottom */
.footer-bottom {
  background-color: #1a252f;
  padding: 20px 0;
}

.footer-bottom-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.footer-bottom-links {
  display: flex;
  gap: 20px;
}

.bottom-link {
  color: #7f8c8d;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.bottom-link:hover {
  color: #3498db;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .footer-container {
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 30px 15px;
  }
  
  .newsletter-form {
    flex-direction: column;
    gap: 10px;
  }
  
  .newsletter-input,
  .newsletter-btn {
    width: 100%;
  }
  
  .footer-bottom-container {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .footer-bottom-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: 25px 10px;
  }
  
  .social-links {
    justify-content: center;
  }
  
  .contact-item {
    font-size: 14px;
  }
}
