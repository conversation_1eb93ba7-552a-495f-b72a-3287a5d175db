/* ================== HEADER ================== */
.header {
  background-color: #ffffff;
  box-shadow: 0 1px 4px rgba(0,0,0,0.08);
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
}

/* Container */
.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ================== LOGO ================== */
.header-logo h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #3498db;
  letter-spacing: 0.5px;
}

/* ================== NAVIGATION ================== */
.header-nav {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 20px;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.3s, border-bottom 0.3s;
  padding-bottom: 2px;
}

.nav-link:hover {
  color: #3498db;
  border-bottom: 2px solid #3498db;
}

/* ================== SEARCH ================== */
.header-search {
  display: flex;
  align-items: center;
  background: #f2f6fa;
  border-radius: 18px;
  padding: 4px 10px;
  min-width: 220px;
}

.search-input {
  border: none;
  background: transparent;
  outline: none;
  flex: 1;
  padding: 4px;
  font-size: 13px;
}

.search-btn {
  border: none;
  background: transparent;
  cursor: pointer;
  color: #3498db;
  font-size: 16px;
}

/* ================== ACTIONS ================== */
.header-actions {
  display: flex;
  align-items: center;
  gap: 14px;
}

.cart-link {
  text-decoration: none;
  position: relative;
}

.cart-icon {
  font-size: 20px;
  color: #3498db;
}

.cart-badge {
  position: absolute;
  top: -5px;
  right: -7px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

/* ================== USER MENU ================== */
.user-dropdown {
  position: relative;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
  font-size: 14px;
}

.user-btn:hover {
  background-color: #f2f6fa;
}

.user-avatar {
  font-size: 18px;
  color: #3498db;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 105%;
  right: 0;
  background: #fff;
  border: 1px solid #e1e5eb;
  border-radius: 6px;
  box-shadow: 0 3px 6px rgba(0,0,0,0.08);
  min-width: 140px;
  z-index: 1001;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 8px 12px;
  text-decoration: none;
  color: #333;
  font-size: 13px;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s;
}

.dropdown-item:hover {
  background-color: #f2f6fa;
  color: #3498db;
}

/* ================== AUTH ================== */
.auth-buttons {
  display: flex;
  gap: 12px;
}

.auth-button {
  padding: 8px 18px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500; /* không quá đậm */
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
}

/* Nút đăng nhập */
.auth-button.login {
  background: linear-gradient(90deg, #007bff, #0056d2);
  color: white;
}

.auth-button.login:hover {
  background: linear-gradient(90deg, #0056d2, #003a99);
}

/* Nút đăng ký */
.auth-button.register {
  background: #36a2eb;
  color: white;
}

.auth-button.register:hover {
  background: #1e90ff;
}


/* ================== MOBILE ================== */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.hamburger {
  width: 22px;
  height: 2px;
  background-color: #333;
  margin: 3px 0;
  transition: 0.3s;
}

@media (max-width: 768px) {
  .header-container {
    padding: 8px 12px;
  }
  
  .header-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border-top: 1px solid #e1e5eb;
  }
  
  .nav-open {
    display: block;
  }
  
  .nav-list {
    flex-direction: column;
    padding: 15px;
    gap: 10px;
  }
  
  .header-search {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .auth-buttons {
    flex-direction: column;
    gap: 6px;
  }
  
  .auth-btn {
    padding: 5px 10px;
    font-size: 13px;
  }
}
