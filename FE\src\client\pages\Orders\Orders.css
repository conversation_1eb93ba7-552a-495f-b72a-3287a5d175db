.orders-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.orders-header h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
  font-weight: 700;
}

.continue-shopping-btn {
  background: #3498db;
  color: white;
  padding: 12px 25px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s;
}

.continue-shopping-btn:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

/* Orders List */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.order-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.order-info h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.order-date {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.status-badge {
  color: white;
  padding: 6px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Order Content */
.order-content {
  padding: 25px;
}

.order-products {
  margin-bottom: 20px;
}

.order-product {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f1f1;
}

.order-product:last-child {
  border-bottom: none;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-name {
  font-weight: 500;
  color: #333;
}

.product-quantity {
  font-size: 14px;
  color: #666;
}

.product-price {
  font-weight: 600;
  color: #e74c3c;
  font-size: 16px;
}

.order-details {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 30px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.delivery-info h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.delivery-info p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.order-total {
  text-align: right;
}

.order-total h4 {
  margin: 0;
  color: #e74c3c;
  font-size: 20px;
  font-weight: 700;
}

/* Order Actions */
.order-actions {
  display: flex;
  gap: 15px;
  padding: 20px 25px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.btn-detail,
.btn-track {
  padding: 10px 20px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s;
  text-align: center;
}

.btn-detail {
  background: #6c757d;
  color: white;
}

.btn-detail:hover {
  background: #5a6268;
}

.btn-track {
  background: #17a2b8;
  color: white;
}

.btn-track:hover {
  background: #138496;
}

/* No Orders */
.no-orders {
  background: white;
  border-radius: 12px;
  padding: 60px 30px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.no-orders-content {
  max-width: 400px;
  margin: 0 auto;
}

.no-orders-icon {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-orders h2 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 24px;
}

.no-orders p {
  margin: 0 0 30px 0;
  color: #666;
  font-size: 16px;
  line-height: 1.5;
}

.start-shopping-btn {
  display: inline-block;
  background: #28a745;
  color: white;
  padding: 15px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s;
}

.start-shopping-btn:hover {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .orders-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 20px;
  }
  
  .orders-header h1 {
    font-size: 24px;
  }
  
  .order-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 20px;
  }
  
  .order-content {
    padding: 20px;
  }
  
  .order-details {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .order-total {
    text-align: left;
  }
  
  .order-actions {
    flex-direction: column;
    gap: 10px;
    padding: 20px;
  }
  
  .no-orders {
    padding: 40px 20px;
  }
  
  .no-orders-icon {
    font-size: 60px;
  }
}

@media (max-width: 480px) {
  .order-product {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .product-price {
    align-self: flex-end;
  }
}
