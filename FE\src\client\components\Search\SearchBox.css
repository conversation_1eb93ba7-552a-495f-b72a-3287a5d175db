.search-box {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.search-input-container {
  display: flex;
  align-items: center;
  background: #f2f6fa;
  border-radius: 25px;
  padding: 8px 15px;
  border: 2px solid transparent;
  transition: border-color 0.3s, box-shadow 0.3s;
  width: 100%; 
  box-sizing: border-box;
}

.search-input-container:focus-within {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-input {
  border: none;
  background: transparent;
  outline: none;
  flex: 1;
  padding: 5px;
  font-size: 14px;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 5px;
  color: #3498db;
  font-size: 16px;
  transition: color 0.3s;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover:not(:disabled) {
  color: #2980b9;
  background: rgba(52, 152, 219, 0.1);
}

.search-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

/* Suggestions Dropdown */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 5px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.2s;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background-color: #f8f9fa;
}

.suggestion-item:last-of-type {
  border-bottom: none;
}

.suggestion-image {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  border-radius: 4px;
  overflow: hidden;
}

.suggestion-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.suggestion-content {
  flex: 1;
  min-width: 0;
}

.suggestion-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.suggestion-title .highlight {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;
}

.suggestion-price {
  font-size: 13px;
  color: #e74c3c;
  font-weight: 600;
}

.suggestion-footer {
  padding: 10px 15px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.view-all-btn {
  width: 100%;
  background: none;
  border: none;
  color: #3498db;
  font-size: 14px;
  font-weight: 500;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.view-all-btn:hover {
  background: rgba(52, 152, 219, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .search-box {
    max-width: 100%;
  }
  
  .search-input-container {
    padding: 10px 15px;
  }
  
  .search-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .search-suggestions {
    max-height: 300px;
  }
  
  .suggestion-item {
    padding: 15px;
  }
  
  .suggestion-image {
    width: 50px;
    height: 50px;
  }
  
  .suggestion-title {
    font-size: 15px;
    white-space: normal;
    overflow: visible;
    text-overflow: initial;
    line-height: 1.3;
  }
}

/* Animation */
.search-suggestions {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.search-btn.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
