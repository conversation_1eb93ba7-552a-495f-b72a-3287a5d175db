/* Product Page Styles */
.product-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Page Header */
.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #333;
  margin: 0 0 15px 0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;
}

.breadcrumb .separator {
  color: #ccc;
}

.breadcrumb span:last-child {
  color: #333;
  font-weight: 500;
}

/* Filter Tags */
.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 20px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border-radius: 8px;
  border: 1px solid #d4edda;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  padding: 8px 12px;
  border-radius: 20px;
  border: 1px solid #d4edda;
  font-size: 14px;
  color: #333;
}

.remove-tag {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.remove-tag:hover {
  background: #f8f9fa;
  color: #333;
}

/* Product Layout */
.product-layout {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 30px;
}

/* Left Sidebar */
.product-sidebar {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.sidebar-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #28a745;
}

/* Category Section */
.category-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.category-item:hover {
  background: #f8f9fa;
  transform: translateX(5px);
}

.category-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e8f5e8;
  border-radius: 8px;
}

.icon-placeholder {
  font-size: 20px;
}

.category-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.category-name {
  font-weight: 500;
  color: #333;
}

.category-count {
  font-size: 12px;
  color: #666;
}

/* Price Filter Section */
.price-filter {
  margin-bottom: 25px;
}

.price-range {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 14px;
  color: #666;
}

.price-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e9ecef;
  outline: none;
  -webkit-appearance: none;
}

.price-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #28a745;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.price-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #28a745;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* Filter Groups */
.filter-group {
  margin-bottom: 20px;
}

.filter-group h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 12px 0;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.3s ease;
}

.filter-option:hover {
  color: #28a745;
}

.filter-option input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #28a745;
}

/* Filter Button */
.filter-btn {
  width: 100%;
  padding: 12px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
}

.filter-btn:hover {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.filter-icon {
  font-size: 18px;
}

/* Decorative Image */
.decorative-image {
  text-align: center;
  margin-top: 20px;
}

.decorative-image img {
  width: 100%;
  max-width: 200px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* New Products Section */
.new-products-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.new-product-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.new-product-item:hover {
  background: #f8f9fa;
  transform: translateX(5px);
}

.new-product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.new-product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.new-product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.new-product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.new-product-price {
  font-size: 16px;
  font-weight: 600;
  color: #28a745;
}

.new-product-rating {
  font-size: 14px;
  color: #ffc107;
}

/* Main Content */
.product-main {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* Product Toolbar */
.product-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.results-info {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.toolbar-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.view-toggle {
  display: flex;
  gap: 5px;
}

.view-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 18px;
}

.view-btn.active {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.view-btn:hover:not(.active) {
  background: #f8f9fa;
  border-color: #28a745;
}

.items-per-page,
.sort-by {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.items-per-page select,
.sort-by select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.items-per-page select:focus,
.sort-by select:focus {
  outline: none;
  border-color: #28a745;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  padding: 25px;
}

.products-grid.list-view {
  grid-template-columns: 1fr;
}

/* Product Card */
.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #28a745;
}

/* Product Badge */
.product-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.product-badge.hot {
  background: #dc3545;
}

.product-badge.sale {
  background: #007bff;
}

.product-badge.new {
  background: #28a745;
}

.product-badge.discount {
  background: #fd7e14;
}

/* Product Image - Takes full width and 2/3 height */
.product-image {
  flex: 2; /* Takes 2/3 of the card height */
  width: 100%;
  overflow: hidden;
  background: #f8f9fa;
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

/* Product Info - Takes 1/3 height */
.product-info {
  flex: 1; /* Takes 1/3 of the card height */
  padding: 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-category {
  font-size: 12px;
  color: #28a745;
  font-weight: 600;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  background: #e8f5e8;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 42px;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.stars {
  font-size: 16px;
}

.rating-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.product-brand {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-style: italic;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.current-price {
  font-size: 18px;
  font-weight: 700;
  color: #28a745;
}

.old-price {
  font-size: 16px;
  color: #999;
  text-decoration: line-through;
  font-weight: 500;
}

.add-to-cart-btn {
  width: 100%;
  padding: 12px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.add-to-cart-btn:hover {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.cart-icon {
  font-size: 16px;
}

/* List View Styles */
.products-grid.list-view .product-card {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 20px;
}

.products-grid.list-view .product-image {
  height: 200px;
}

.products-grid.list-view .product-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

/* No Products */
.no-products {
  text-align: center;
  padding: 60px 30px;
  color: #666;
}

.no-products p {
  font-size: 18px;
  margin: 0;
}

/* Scroll to Top Button */
.scroll-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
  z-index: 1000;
}

.scroll-to-top:hover {
  background: #218838;
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.arrow-up {
  font-weight: bold;
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #28a745;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .product-layout {
    grid-template-columns: 280px 1fr;
    gap: 20px;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .product-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .product-sidebar {
    order: 2;
  }
  
  .product-main {
    order: 1;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
    padding: 15px;
  }
  
  .product-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .toolbar-controls {
    justify-content: space-between;
  }
  
  .page-header {
    padding: 20px;
  }
  
  .filter-tags {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px;
  }
  
  .product-card {
    max-width: 280px;
    margin: 0 auto;
  }
    
  .scroll-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
  }
}

.category-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 6px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s;
}

.category-item:hover {
  background-color: #f0f0f0;
}

.category-item.selected {
  background-color: #e6ffef; /* màu đỏ nhạt */
  border-left: 4px solid rgb(134, 179, 160);
}

.category-icon {
  margin-right: 8px;
}

.category-info {
  display: flex;
  flex-direction: column;
}

