.admin-header {
  height: 70px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  position: fixed;
  top: 0;
  left: 280px;
  right: 0;
  z-index: 999;
  transition: left 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admin-header.sidebar-collapsed {
  left: 70px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.sidebar-toggle-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #6c757d;
  transition: all 0.3s ease;
}

.sidebar-toggle-btn:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.breadcrumb-item {
  color: #6c757d;
}

.breadcrumb-item.active {
  color: #495057;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #adb5bd;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Search */
.header-search {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25px;
  padding: 8px 15px;
  min-width: 250px;
}

.search-input {
  border: none;
  background: transparent;
  outline: none;
  flex: 1;
  padding: 5px;
  font-size: 14px;
}

.search-btn {
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 5px;
  color: #6c757d;
}

/* Notifications */
.notification-dropdown {
  position: relative;
}

.notification-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  position: relative;
  transition: background-color 0.3s;
}

.notification-btn:hover {
  background-color: #f8f9fa;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.notification-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  width: 320px;
  z-index: 1001;
  max-height: 400px;
  overflow: hidden;
}

.notification-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-header h4 {
  margin: 0;
  font-size: 16px;
  color: #495057;
}

.mark-all-read {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 12px;
}

.notification-list {
  max-height: 250px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 20px;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-icon {
  font-size: 16px;
  margin-right: 12px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-message {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #495057;
}

.notification-time {
  font-size: 12px;
  color: #6c757d;
}

.notification-footer {
  padding: 10px 20px;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.view-all-btn {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 10px;
}

.quick-action-btn {
  background: none;
  border: 1px solid #e9ecef;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.quick-action-btn:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

/* Profile */
.profile-dropdown {
  position: relative;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.profile-btn:hover {
  background-color: #f8f9fa;
}

.profile-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.profile-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.profile-name {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.profile-role {
  font-size: 12px;
  color: #6c757d;
}

.dropdown-arrow {
  font-size: 10px;
  color: #6c757d;
}

.profile-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  min-width: 180px;
  z-index: 1001;
  padding: 8px 0;
}

.profile-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 10px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.3s;
}

.profile-menu-item:hover {
  background-color: #f8f9fa;
}

.profile-menu-item.logout {
  color: #dc3545;
}

.profile-menu-item.logout:hover {
  background-color: #f8d7da;
}

.menu-icon {
  font-size: 14px;
  width: 16px;
}

.menu-divider {
  margin: 8px 0;
  border: none;
  border-top: 1px solid #e9ecef;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .admin-header {
    left: 0;
    padding: 0 15px;
  }
  
  .admin-header.sidebar-collapsed {
    left: 0;
  }
  
  .header-search {
    display: none;
  }
  
  .quick-actions {
    display: none;
  }
  
  .profile-info {
    display: none;
  }
}
