{"name": "be", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon --inspect index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dateformat": "^5.0.3", "dotenv": "^16.4.7", "express": "^4.21.2", "express-flash": "^0.0.2", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "method-override": "^3.0.0", "mongodb": "^6.14.2", "mongoose": "^8.10.1", "mongoose-slug-updater": "^3.3.0", "nodemailer": "^6.10.0", "qs": "^6.14.0", "tinymce": "^7.7.0"}, "devDependencies": {"nodemon": "^3.1.9"}}