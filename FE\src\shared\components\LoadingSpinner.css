.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding: 20px;
}

.loading-spinner {
  border-radius: 50%;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner.medium {
  width: 40px;
  height: 40px;
  border-width: 3px;
}

.loading-spinner.large {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

.loading-text {
  color: #666;
  font-size: 14px;
  margin: 0;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
