import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet';
import { OrderService } from '../../services/orderService';
import { toast } from 'react-toastify';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import './OrderTracking.css';

// Fix for default markers in React Leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom icons for different markers
const deliveryIcon = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-blue.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const destinationIcon = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const OrderTracking = () => {
  const { orderId } = useParams();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [deliveryLocation, setDeliveryLocation] = useState(null);

  useEffect(() => {
    fetchOrderDetail();
    // Simulate delivery tracking updates
    const interval = setInterval(() => {
      updateDeliveryLocation();
    }, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  }, [orderId]);

  const fetchOrderDetail = async () => {
    try {
      setLoading(true);
      const response = await OrderService.getOrderDetail(orderId);
      
      if (response?.code === 200) {
        setOrder(response.order);
        // Initialize delivery location (simulate starting point)
        if (response.order?.userInfo?.toadoa?.coordinates) {
          const destination = response.order.userInfo.toadoa.coordinates;
          // Start delivery from a point 5km away (simulated)
          setDeliveryLocation([
            destination[0] - 0.05, // Longitude offset
            destination[1] - 0.05  // Latitude offset
          ]);
        }
      }
    } catch (error) {
      console.error('Error fetching order:', error);
      toast.error('Không thể tải thông tin đơn hàng');
    } finally {
      setLoading(false);
    }
  };

  const updateDeliveryLocation = () => {
    if (order?.userInfo?.toadoa?.coordinates && deliveryLocation) {
      const destination = order.userInfo.toadoa.coordinates;
      const current = deliveryLocation;
      
      // Move delivery location closer to destination (simulate movement)
      const deltaLng = (destination[0] - current[0]) * 0.1;
      const deltaLat = (destination[1] - current[1]) * 0.1;
      
      if (Math.abs(deltaLng) > 0.001 || Math.abs(deltaLat) > 0.001) {
        setDeliveryLocation([
          current[0] + deltaLng,
          current[1] + deltaLat
        ]);
      }
    }
  };

  const getStatusSteps = () => {
    const steps = [
      { key: 'pending', label: 'Chờ xác nhận', icon: '📋' },
      { key: 'confirmed', label: 'Đã xác nhận', icon: '✅' },
      { key: 'shipping', label: 'Đang giao hàng', icon: '🚚' },
      { key: 'delivered', label: 'Đã giao hàng', icon: '📦' }
    ];

    const currentIndex = steps.findIndex(step => step.key === order?.status);
    
    return steps.map((step, index) => ({
      ...step,
      completed: index <= currentIndex,
      active: index === currentIndex
    }));
  };

  const calculateOrderTotal = (products) => {
    return products.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  };

  if (loading) {
    return (
      <div className="order-tracking-page">
        <div className="container">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Đang tải thông tin đơn hàng...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="order-tracking-page">
        <div className="container">
          <div className="error-container">
            <h2>Không tìm thấy đơn hàng</h2>
            <Link to="/orders" className="back-btn">Quay lại danh sách đơn hàng</Link>
          </div>
        </div>
      </div>
    );
  }

  const destination = order.userInfo?.toadoa?.coordinates;
  const mapCenter = destination ? [destination[1], destination[0]] : [21.028511, 105.854444];

  return (
    <div className="order-tracking-page">
      <div className="container">
        <div className="tracking-header">
          <div className="header-info">
            <h1>Theo dõi đơn hàng</h1>
            <p>Đơn hàng #{order._id.slice(-8)}</p>
          </div>
          <Link to="/orders" className="back-btn">
            ← Quay lại
          </Link>
        </div>

        <div className="tracking-content">
          {/* Order Status Timeline */}
          <div className="status-timeline">
            <h2>Trạng thái đơn hàng</h2>
            <div className="timeline">
              {getStatusSteps().map((step, index) => (
                <div 
                  key={step.key} 
                  className={`timeline-step ${step.completed ? 'completed' : ''} ${step.active ? 'active' : ''}`}
                >
                  <div className="step-icon">{step.icon}</div>
                  <div className="step-content">
                    <h3>{step.label}</h3>
                    {step.active && (
                      <p className="step-time">
                        {new Date().toLocaleString('vi-VN')}
                      </p>
                    )}
                  </div>
                  {index < getStatusSteps().length - 1 && (
                    <div className="step-connector"></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Map for shipping status */}
          {order.status === 'shipping' && destination && (
            <div className="delivery-map">
              <h2>Vị trí giao hàng</h2>
              <div className="map-container">
                <MapContainer
                  center={mapCenter}
                  zoom={13}
                  style={{ height: '400px', width: '100%' }}
                >
                  <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  />
                  
                  {/* Destination marker */}
                  <Marker 
                    position={[destination[1], destination[0]]} 
                    icon={destinationIcon}
                  >
                    <Popup>
                      <div>
                        <strong>Địa chỉ giao hàng</strong><br/>
                        {order.userInfo.address}
                      </div>
                    </Popup>
                  </Marker>

                  {/* Delivery person marker */}
                  {deliveryLocation && (
                    <Marker 
                      position={[deliveryLocation[1], deliveryLocation[0]]} 
                      icon={deliveryIcon}
                    >
                      <Popup>
                        <div>
                          <strong>Shipper</strong><br/>
                          Đang trên đường giao hàng
                        </div>
                      </Popup>
                    </Marker>
                  )}

                  {/* Route line */}
                  {deliveryLocation && (
                    <Polyline
                      positions={[
                        [deliveryLocation[1], deliveryLocation[0]],
                        [destination[1], destination[0]]
                      ]}
                      color="blue"
                      weight={3}
                      opacity={0.7}
                      dashArray="10, 10"
                    />
                  )}
                </MapContainer>
              </div>
              <div className="map-legend">
                <div className="legend-item">
                  <span className="legend-icon blue">📍</span>
                  <span>Shipper</span>
                </div>
                <div className="legend-item">
                  <span className="legend-icon red">📍</span>
                  <span>Địa chỉ giao hàng</span>
                </div>
              </div>
            </div>
          )}

          {/* Order Details */}
          <div className="order-details">
            <h2>Chi tiết đơn hàng</h2>
            <div className="details-grid">
              <div className="customer-info">
                <h3>Thông tin khách hàng</h3>
                <p><strong>Họ tên:</strong> {order.userInfo.fullName}</p>
                <p><strong>Số điện thoại:</strong> {order.userInfo.phone}</p>
                <p><strong>Địa chỉ:</strong> {order.userInfo.address}</p>
              </div>

              <div className="order-summary">
                <h3>Sản phẩm</h3>
                {order.products.map((item, index) => (
                  <div key={index} className="product-item">
                    <span>Sản phẩm ID: {item.product_id}</span>
                    <span>x{item.quantity}</span>
                    <span>₫{(item.price * item.quantity).toLocaleString()}</span>
                  </div>
                ))}
                <div className="total-amount">
                  <strong>Tổng cộng: ₫{calculateOrderTotal(order.products).toLocaleString()}</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderTracking;
