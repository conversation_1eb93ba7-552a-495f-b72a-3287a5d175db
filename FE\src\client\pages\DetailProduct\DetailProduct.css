/* Detail Product Page Styles */
.detail-product-page {
  padding: 20px 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px;
  font-size: 14px;
  color: #666;
}

.breadcrumb a {
  color: #007bff;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.breadcrumb a:focus {
  outline: none;
  text-decoration: underline;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.breadcrumb span {
  color: #999;
}

/* Main Product Section */
.product-main-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 50px;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Product Images */
.product-images {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-image-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
}

.main-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  display: block;
}

.zoom-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #333;
  transition: all 0.3s ease;
  line-height: 1;
}

.zoom-btn:hover {
  background: white;
  transform: scale(1.1);
}

.zoom-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.thumbnail-images {
  display: flex;
  gap: 15px;
}

.thumbnail-item {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

.thumbnail-item:hover {
  border-color: #007bff;
}

.thumbnail-item:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Product Details */
.product-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sale-badge {
  display: inline-block;
  background: #ff6b6b;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  align-self: flex-start;
}

.product-title {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  line-height: 1.3;
  margin: 0;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 18px;
}

.star.filled {
  color: #ffd700;
}

.review-count {
  color: #666;
  font-size: 14px;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.current-price {
  font-size: 32px;
  font-weight: 700;
  color: #28a745;
}

.original-price {
  font-size: 20px;
  color: #999;
  text-decoration: line-through;
}

.discount-percentage {
  background: #ff6b6b;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.product-description {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Size Selection */
.size-selection {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.size-selection label {
  font-weight: 600;
  color: #333;
}

.size-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.size-option {
  padding: 8px 16px;
  border: 2px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.size-option:hover {
  border-color: #007bff;
}

.size-option:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.size-option.selected {
  border-color: #28a745;
  background: #28a745;
  color: white;
}

/* Purchase Actions */
.purchase-actions {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.quantity-selector {
  display: flex;
  align-items: center;
  border: 2px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.quantity-selector button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f8f9fa;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.quantity-selector button:hover:not(:disabled) {
  background: #e9ecef;
}

.quantity-selector button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.quantity-selector button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-selector input {
  width: 60px;
  height: 40px;
  border: none;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
}

.quantity-selector input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.add-to-cart-and-buy-now-container {
  display :flex;
  gap: 10px;
}

.buy-now-btn {
  background-color: rgb(224, 78, 15);
  color: white;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background-color 0.3s ease;
}

.add-to-cart-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background-color 0.3s ease;
}

.add-to-cart-btn span {
  font-size: 18px;
  line-height: 1;
}

.add-to-cart-btn:hover {
  background: #218838;
}

.add-to-cart-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.wishlist-btn,
.share-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #666;
  transition: all 0.3s ease;
  line-height: 1;
}

.wishlist-btn:hover,
.share-btn:hover {
  border-color: #007bff;
  color: #007bff;
}

.wishlist-btn:focus,
.share-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Product Metadata */
.product-metadata {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metadata-item .label {
  font-weight: 600;
  color: #666;
}

.metadata-item .value {
  color: #333;
  font-weight: 500;
}

/* Product Tabs */
.product-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 50px;
  overflow: hidden;
}

.tab-headers {
  display: flex;
  border-bottom: 2px solid #f8f9fa;
}

.tab-header {
  flex: 1;
  padding: 20px;
  border: none;
  background: white;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  color: #666;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-header:hover {
  color: #007bff;
}

.tab-header:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.tab-header.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.tab-content {
  padding: 30px;
}

.description-content h4 {
  color: #333;
  margin: 25px 0 15px 0;
  font-size: 18px;
}

.description-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.description-content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.description-content li {
  color: #666;
  line-height: 1.6;
  margin-bottom: 8px;
}

/* Related Products */
.related-products {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

.related-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.related-product-card {
  position: relative;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.related-product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.related-product-card:focus-within {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.product-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  z-index: 1;
}

.product-badge.hot {
  background: #ff6b6b;
}

.product-badge.new {
  background: #28a745;
}

.product-badge.discount {
  background: #007bff;
}

.related-product-image {
  height: 200px;
  overflow: hidden;
}

.related-product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.related-product-card:hover .related-product-image img {
  transform: scale(1.1);
}

.related-product-card:focus-within .related-product-image img {
  transform: scale(1.05);
}

.related-product-card:focus-within {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.related-product-info {
  padding: 20px;
}

.related-product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
  line-height: 1.4;
}

.related-product-rating {
  margin-bottom: 10px;
}

.related-product-rating .star {
  font-size: 14px;
}

.related-product-price {
  display: flex;
  align-items: center;
  gap: 10px;
}

.related-product-price .current-price {
  font-size: 18px;
  font-weight: 700;
  color: #28a745;
}

.related-product-price .original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-main-section {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 20px;
  }
  
  .purchase-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .product-metadata {
    grid-template-columns: 1fr;
  }
  
  .tab-headers {
    flex-direction: column;
  }
  
  .related-products-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }
  
  .product-main-section {
    padding: 15px;
  }
  
  .product-title {
    font-size: 24px;
  }
  
  .current-price {
    font-size: 28px;
  }
  
  .thumbnail-images {
    gap: 10px;
  }
  
  .thumbnail-item {
    width: 60px;
    height: 60px;
  }
}
