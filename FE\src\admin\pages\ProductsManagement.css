.products-management-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

/* Filters */
.filters-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.filter-group {
  display: flex;
  gap: 15px;
}

.filter-select {
  padding: 12px 15px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  min-width: 150px;
}

.results-info {
  margin-bottom: 20px;
  color: #6c757d;
  font-size: 14px;
}

/* Product Table */
.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.product-name {
  font-weight: 600;
  color: #495057;
  margin-bottom: 4px;
}

.product-id {
  font-size: 12px;
  color: #6c757d;
}

.product-price {
  font-weight: 600;
  color: #28a745;
}

.stock-count {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  background: #d4edda;
  color: #155724;
}

.stock-count.out-of-stock {
  background: #f8d7da;
  color: #721c24;
}

/* Status badges */
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-active {
  background: #d4edda;
  color: #155724;
}

.status-inactive {
  background: #f8d7da;
  color: #721c24;
}

.status-out-of-stock {
  background: #fff3cd;
  color: #856404;
}

.status-default {
  background: #e9ecef;
  color: #495057;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.dropdown {
  position: relative;
}

.dropdown-toggle {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  min-width: 120px;
  z-index: 1000;
  display: none;
}

.dropdown:hover .dropdown-menu {
  display: block;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
  transition: background-color 0.3s;
}

.dropdown-item:hover {
  background: #f8f9fa;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.no-data p {
  margin: 0;
  font-size: 16px;
}

/* Bulk actions */
.bulk-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.bulk-actions.hidden {
  display: none;
}

.bulk-info {
  color: #495057;
  font-weight: 500;
}

.bulk-buttons {
  display: flex;
  gap: 10px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 30px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.pagination-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.pagination-info {
  color: #6c757d;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 1024px) {
  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .filter-select {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .page-actions {
    display: flex;
    justify-content: center;
  }
  
  .admin-table {
    font-size: 12px;
  }
  
  .admin-table th,
  .admin-table td {
    padding: 8px 4px;
  }
  
  .product-info {
    gap: 8px;
  }
  
  .product-image {
    width: 40px;
    height: 40px;
  }
  
  .product-name {
    font-size: 13px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .btn-sm {
    padding: 4px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .filter-group {
    flex-direction: column;
    gap: 10px;
  }
  
  .table-responsive {
    overflow-x: auto;
  }
  
  .admin-table {
    min-width: 600px;
  }
  
  .pagination {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .pagination-btn {
    padding: 6px 10px;
    font-size: 12px;
  }
}
