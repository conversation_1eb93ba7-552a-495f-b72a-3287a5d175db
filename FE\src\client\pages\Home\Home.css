/* ==== Global ==== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-section {
  padding: 60px 0;
}

.section-title {
  font-size: 28px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 40px;
  color: #222;
}

/* ==== Hero Section ==== */
.hero-section {
  background: linear-gradient(135deg, #f48888, #f8cdda);
  color: #fff;
  padding: 80px 0;
}

.hero-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.hero-text {
  flex: 1 1 500px;
  margin-bottom: 20px;
}

.hero-title {
  font-size: 40px;
  font-weight: 700;
  margin-bottom: 16px;
}

.hero-subtitle {
  font-size: 18px;
  margin-bottom: 24px;
}

.hero-actions .btn {
  display: inline-block;
  margin-right: 12px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: 0.3s ease;
  text-decoration: none;
}

.btn-primary {
  background: #fff;
  color: #f48888;
}

.btn-primary:hover {
  background: #f0f0f0;
}

.btn-outline {
  border: 2px solid #fff;
  color: #fff;
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.2);
}

.hero-image {
  flex: 1 1 400px;
  text-align: center;
}

.hero-image img {
  max-width: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* ==== Categories ==== */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 24px;
}

.category-card {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.category-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.15);
}

.category-icon {
  font-size: 40px;
  margin-bottom: 12px;
}

.category-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.category-count {
  font-size: 14px;
  color: #777;
}

/* ==== Features ==== */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 24px;
}

.feature-item {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.feature-icon {
  font-size: 36px;
  margin-bottom: 12px;
}

.feature-item h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: #666;
}

/* ==== Loading ==== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ddd;
  border-top: 4px solid #f48888;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ==== Responsive ==== */
@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
  }

  .hero-text {
    margin-bottom: 30px;
  }
}
/* Container cho grid sản phẩm */
/* ==== Grid sản phẩm ==== */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px;
  background: #fff; /* nền trắng */
}

/* ==== Card ==== */
.product-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  position: relative;
}
.product-card img {
  width: 100%;
  height: 200px;   
  object-fit: cover; 
  border-radius: 8px 8px 0 0; 
  display: block;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.12);
}

/* ==== Ảnh sản phẩm ==== */
.product-image {
  width: 100%;
  height: 220px; /* to hơn */
  object-fit: cover;
  display: block;
}

/* ==== Nội dung ==== */
.product-info {
  padding: 10px 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.product-title {
  font-size: 14px;
  font-weight: 400;
  color: #333;
  line-height: 1.4;
  height: 38px; /* fix cao 2 dòng */
  overflow: hidden;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: #d0011b; /* màu đỏ shopee */
}

.product-sold {
  font-size: 12px;
  color: #777;
}

/* ==== Discount Tag ==== */
.discount-tag {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ffecec;
  color: #d0011b;
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

