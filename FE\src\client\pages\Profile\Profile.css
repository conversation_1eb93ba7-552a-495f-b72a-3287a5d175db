.profile-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px 0;
}

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 20px;
  padding: 0 20px;
}

/* Sidebar Styles */
.profile-sidebar {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  height: fit-content;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
}

.user-name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.user-edit {
  color: #666;
  font-size: 12px;
  margin-left: auto;
}

.sidebar-nav {
  padding: 0;
}

.nav-section {
  padding: 10px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #333;
  font-size: 14px;
}

.nav-item:hover {
  background-color: #f8f9fa;
}

.nav-item.special {
  position: relative;
}

.nav-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.badge {
  background: #ff4757;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
  font-weight: 600;
}

.nav-group {
  margin: 5px 0;
}

.nav-group-title {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.nav-subitems {
  padding-left: 52px;
}

.nav-subitem {
  padding: 8px 20px 8px 0;
  cursor: pointer;
  transition: color 0.2s;
  color: #666;
  font-size: 14px;
  position: relative;
}

.nav-subitem:hover {
  color: #ee4d2d;
}

.nav-subitem.active {
  color: #ee4d2d;
  font-weight: 500;
}

.nav-subitem.active::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background: #ee4d2d;
  border-radius: 2px;
}

/* Main Content Styles */
.profile-main {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 30px;
}

.content-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.content-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.content-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.profile-form,
.password-form {
  max-width: 800px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
}

.form-left {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
  min-width: 120px;
}

.form-control {
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: #ee4d2d;
  box-shadow: 0 0 0 2px rgba(238, 77, 45, 0.1);
}

.form-control:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.form-control.error {
  border-color: #ff4757;
}

.error-message {
  color: #ff4757;
  font-size: 12px;
  margin-top: 4px;
}

.error-alert {
  background: #fff2f0;
  color: #ff4757;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid #ffccc7;
  font-size: 14px;
  margin-bottom: 20px;
}

.success-alert {
  background: #f6ffed;
  color: #52c41a;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid #b7eb8f;
  font-size: 14px;
  margin-bottom: 20px;
}

/* Email and Phone Groups */
.email-group,
.phone-group {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.email-group .form-control,
.phone-group .form-control {
  flex: 1;
}

.change-btn {
  background: transparent;
  border: 1px solid #ee4d2d;
  color: #ee4d2d;
  padding: 12px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.change-btn:hover {
  background: #ee4d2d;
  color: white;
}

/* Gender Group */
.gender-group {
  display: flex;
  gap: 20px;
  align-items: center;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.radio-label input[type="radio"] {
  margin: 0;
  accent-color: #ee4d2d;
}

/* Date Group */
.date-group {
  display: flex;
  gap: 10px;
}

.form-select {
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  flex: 1;
}

.form-select:focus {
  outline: none;
  border-color: #ee4d2d;
  box-shadow: 0 0 0 2px rgba(238, 77, 45, 0.1);
}

/* Save Button */
.save-btn {
  background: #ee4d2d;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.save-btn:hover:not(:disabled) {
  background: #d73527;
  transform: translateY(-1px);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Avatar Section */
.form-right {
  display: flex;
  justify-content: center;
}

.avatar-section {
  text-align: center;
}

.avatar-container {
  margin-bottom: 20px;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #f0f0f0;
  background: #f8f9fa;
}

.avatar-btn {
  display: inline-block;
  background: transparent;
  border: 1px solid #d9d9d9;
  color: #333;
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 15px;
}

.avatar-btn:hover {
  border-color: #ee4d2d;
  color: #ee4d2d;
}

.avatar-info {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.avatar-info p {
  margin: 4px 0;
}

/* Coming Soon */
.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.coming-soon p {
  font-size: 16px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 0 15px;
  }

  .profile-sidebar {
    order: 2;
  }

  .profile-main {
    order: 1;
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .form-right {
    order: -1;
  }

  .nav-subitems {
    padding-left: 40px;
  }

  .sidebar-header {
    padding: 15px;
  }

  .user-avatar {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .user-edit {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .profile-page {
    padding: 10px 0;
  }

  .profile-container {
    padding: 0 10px;
  }

  .profile-main {
    padding: 15px;
  }

  .content-header h2 {
    font-size: 20px;
  }

  .email-group,
  .phone-group {
    flex-direction: column;
  }

  .change-btn {
    align-self: flex-start;
  }

  .gender-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .date-group {
    flex-direction: column;
  }

  .profile-avatar {
    width: 100px;
    height: 100px;
  }

  .nav-item {
    padding: 10px 15px;
  }

  .nav-group-title {
    padding: 10px 15px;
  }

  .nav-subitems {
    padding-left: 35px;
  }
}