import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import './PaymentResult.css';

const PaymentResult = () => {
  const [paymentStatus, setPaymentStatus] = useState('processing');
  const [paymentInfo, setPaymentInfo] = useState({});
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    
    // Check MoMo payment result
    const momoResultCode = urlParams.get('resultCode');
    const momoOrderId = urlParams.get('orderId');
    const momoAmount = urlParams.get('amount');
    
    // Check VNPay payment result
    const vnpayResponseCode = urlParams.get('vnp_ResponseCode');
    const vnpayTxnRef = urlParams.get('vnp_TxnRef');
    const vnpayAmount = urlParams.get('vnp_Amount');

    if (momoResultCode !== null) {
      // MoMo payment result
      if (momoResultCode === '0') {
        setPaymentStatus('success');
        setPaymentInfo({
          method: 'MoMo',
          orderId: momoOrderId,
          amount: momoAmount,
          message: 'Thanh toán MoMo thành công!'
        });
      } else {
        setPaymentStatus('failed');
        setPaymentInfo({
          method: 'MoMo',
          orderId: momoOrderId,
          message: 'Thanh toán MoMo thất bại!'
        });
      }
    } else if (vnpayResponseCode !== null) {
      // VNPay payment result
      if (vnpayResponseCode === '00') {
        setPaymentStatus('success');
        setPaymentInfo({
          method: 'VNPay',
          orderId: vnpayTxnRef,
          amount: vnpayAmount ? (parseInt(vnpayAmount) / 100) : 0,
          message: 'Thanh toán VNPay thành công!'
        });
      } else {
        setPaymentStatus('failed');
        setPaymentInfo({
          method: 'VNPay',
          orderId: vnpayTxnRef,
          message: 'Thanh toán VNPay thất bại!'
        });
      }
    } else {
      // No payment parameters found
      setPaymentStatus('failed');
      setPaymentInfo({
        message: 'Không tìm thấy thông tin thanh toán!'
      });
    }
  }, [location.search]);

  const handleContinueShopping = () => {
    navigate('/');
  };

  const handleViewOrders = () => {
    navigate('/orders');
  };

  return (
    <div className="payment-result-page">
      <div className="container">
        <div className="payment-result-card">
          {paymentStatus === 'processing' && (
            <div className="payment-processing">
              <div className="loading-spinner"></div>
              <h2>Đang xử lý thanh toán...</h2>
              <p>Vui lòng đợi trong giây lát</p>
            </div>
          )}

          {paymentStatus === 'success' && (
            <div className="payment-success">
              <div className="success-icon">✅</div>
              <h2>Thanh toán thành công!</h2>
              <p className="success-message">{paymentInfo.message}</p>
              
              <div className="payment-details">
                <div className="detail-row">
                  <span className="label">Phương thức:</span>
                  <span className="value">{paymentInfo.method}</span>
                </div>
                {paymentInfo.orderId && (
                  <div className="detail-row">
                    <span className="label">Mã giao dịch:</span>
                    <span className="value">{paymentInfo.orderId}</span>
                  </div>
                )}
                {paymentInfo.amount && (
                  <div className="detail-row">
                    <span className="label">Số tiền:</span>
                    <span className="value">₫{paymentInfo.amount.toLocaleString()}</span>
                  </div>
                )}
              </div>

              <div className="action-buttons">
                <button 
                  className="btn-primary"
                  onClick={handleViewOrders}
                >
                  Xem đơn hàng
                </button>
                <button 
                  className="btn-secondary"
                  onClick={handleContinueShopping}
                >
                  Tiếp tục mua sắm
                </button>
              </div>
            </div>
          )}

          {paymentStatus === 'failed' && (
            <div className="payment-failed">
              <div className="failed-icon">❌</div>
              <h2>Thanh toán thất bại!</h2>
              <p className="failed-message">{paymentInfo.message}</p>
              
              {paymentInfo.orderId && (
                <div className="payment-details">
                  <div className="detail-row">
                    <span className="label">Mã giao dịch:</span>
                    <span className="value">{paymentInfo.orderId}</span>
                  </div>
                </div>
              )}

              <div className="action-buttons">
                <Link to="/cart" className="btn-primary">
                  Quay lại giỏ hàng
                </Link>
                <button 
                  className="btn-secondary"
                  onClick={handleContinueShopping}
                >
                  Tiếp tục mua sắm
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentResult;
