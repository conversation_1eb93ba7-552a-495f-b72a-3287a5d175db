.auth-footer {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px 0;
  margin-top: auto;
}

.auth-footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.auth-footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.auth-footer-links {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.auth-footer-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.auth-footer-link:hover {
  color: white;
}

.auth-footer-copyright {
  text-align: center;
}

.auth-footer-copyright p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
}

@media (max-width: 768px) {
  .auth-footer-container {
    padding: 0 15px;
  }
  
  .auth-footer-links {
    gap: 20px;
  }
  
  .auth-footer-link {
    font-size: 13px;
  }
}