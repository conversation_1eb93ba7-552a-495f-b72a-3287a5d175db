.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.admin-main {
  flex: 1;
  margin-left: 280px;
  transition: margin-left 0.3s ease;
  display: flex;
  flex-direction: column;
}

.admin-main.sidebar-collapsed {
  margin-left: 70px;
}

.admin-content {
  flex: 1;
  margin-top: 70px;
  padding: 0;
  overflow-x: hidden;
}

.content-wrapper {
  padding: 30px;
  min-height: calc(100vh - 70px);
}

/* Mobile overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

/* Global admin styles */
.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.page-subtitle {
  color: #6c757d;
  font-size: 16px;
  margin: 0;
}

.page-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

/* Card components */
.admin-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  overflow: hidden;
}

.card-header {
  padding: 20px 25px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.card-body {
  padding: 25px;
}

.card-footer {
  padding: 15px 25px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

/* Stats cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #007bff, #0056b3);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.stat-title {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
  margin: 0;
}

.stat-icon {
  font-size: 24px;
  opacity: 0.7;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #495057;
  margin-bottom: 10px;
}

.stat-change {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-change.positive {
  color: #28a745;
}

.stat-change.negative {
  color: #dc3545;
}

/* Tables */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admin-table th {
  background-color: #f8f9fa;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #e9ecef;
}

.admin-table td {
  padding: 15px;
  border-bottom: 1px solid #f8f9fa;
  color: #495057;
}

.admin-table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Buttons */
.btn-admin {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #1e7e34;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid #007bff;
  color: #007bff;
}

.btn-outline:hover {
  background-color: #007bff;
  color: white;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Forms */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-control.error {
  border-color: #dc3545;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-main {
    margin-left: 0;
  }
  
  .admin-main.sidebar-collapsed {
    margin-left: 0;
  }
  
  .content-wrapper {
    padding: 20px 15px;
  }
  
  .mobile-overlay {
    display: block;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .page-actions {
    flex-direction: column;
  }
  
  .btn-admin {
    justify-content: center;
  }
}
