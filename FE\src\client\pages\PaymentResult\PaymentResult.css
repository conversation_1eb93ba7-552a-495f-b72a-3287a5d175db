.payment-result-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 600px;
}

.payment-result-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  text-align: center;
}

/* Processing State */
.payment-processing {
  padding: 20px 0;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 6px solid #f3f3f3;
  border-top: 6px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.payment-processing h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.payment-processing p {
  color: #666;
  font-size: 16px;
}

/* Success State */
.payment-success {
  padding: 20px 0;
}

.success-icon {
  font-size: 80px;
  margin-bottom: 20px;
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.payment-success h2 {
  color: #27ae60;
  margin-bottom: 15px;
  font-size: 28px;
  font-weight: 700;
}

.success-message {
  color: #2c3e50;
  font-size: 18px;
  margin-bottom: 30px;
}

/* Failed State */
.payment-failed {
  padding: 20px 0;
}

.failed-icon {
  font-size: 80px;
  margin-bottom: 20px;
  animation: shake 0.6s ease-out;
}

@keyframes shake {
  0%, 20%, 40%, 60%, 80% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
}

.payment-failed h2 {
  color: #e74c3c;
  margin-bottom: 15px;
  font-size: 28px;
  font-weight: 700;
}

.failed-message {
  color: #2c3e50;
  font-size: 18px;
  margin-bottom: 30px;
}

/* Payment Details */
.payment-details {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  margin: 30px 0;
  text-align: left;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  font-weight: 500;
  color: #666;
}

.detail-row .value {
  font-weight: 600;
  color: #333;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.btn-primary,
.btn-secondary {
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 150px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .payment-result-page {
    padding: 15px;
  }
  
  .payment-result-card {
    padding: 30px 20px;
  }
  
  .success-icon,
  .failed-icon {
    font-size: 60px;
  }
  
  .payment-success h2,
  .payment-failed h2 {
    font-size: 24px;
  }
  
  .success-message,
  .failed-message {
    font-size: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }
  
  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
  
  .payment-details {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .payment-result-card {
    padding: 25px 15px;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .detail-row .value {
    font-size: 14px;
  }
}
