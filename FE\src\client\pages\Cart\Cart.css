.cart-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.cart-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cart-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.cart-content {
  display: flex;
  gap: 20px;
}

.cart-table {
  flex: 1;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cart-table-header {
  display: grid;
  grid-template-columns: 50px minmax(200px, 3fr) 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #666;
  font-size: 14px;
}

.cart-items {
  max-height: 500px;
  overflow-y: auto;
}

.cart-item {
  display: grid;
  grid-template-columns: 50px minmax(200px, 3fr) 1fr 1fr 1fr 1fr;       
  gap: 15px;
  padding: 5px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-checkbox input {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* .item-product {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 15px;
  align-items: center;
}

.product-image {
  width: 100px;          
  height: 100px;        
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;

  display: flex;
  justify-content: center;
  align-items: center;
  background: #f9f9f9;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;  
} */
.item-product {
  display: flex;
  align-items: center;
  gap: 15px;
}

.product-image {
  width: 80px;     /* thumbnail rộng */
  height: 80px;    /* thumbnail cao */
  flex-shrink: 1;  /* không cho co lại */
  border-radius: 8px;
  overflow: hidden;
  background: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}



.product-info {
  flex: 1;
  min-width: 0; 
}

.product-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  white-space: normal;
  word-break: normal;
  overflow-wrap: break-word;
}


.product-tags {
  margin-bottom: 2px;
}

.tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.tag.favorite {
  background: #ff4757;
  color: white;
}

.product-variant {
  font-size: 12px;
  color: #666;
}

.product-variant span {
  color: #333;
}

.item-price {
  text-align: center;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.discount-info {
  margin-top: 5px;
}

.discount-badge {
  background: #ff4757;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.item-quantity {
  display: flex;
  justify-content: center;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f8f9fa;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #666;
}

.quantity-btn:hover:not(:disabled) {
  background: #e9ecef;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-input {
  width: 50px;
  height: 32px;
  border: none;
  text-align: center;
  font-size: 14px;
  outline: none;
}

.item-total {
  text-align: center;
}

.total-price {
  font-size: 16px;
  font-weight: 600;
  color: #ff4757;
}

.item-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.remove-btn, .save-later-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 13px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  color: #ff4757;
  background: #fff5f5;
}

.save-later-btn:hover {
  color: #007bff;
  background: #f0f8ff;
}

.cart-summary {
  width: 300px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  height: fit-content;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 14px;
}

.summary-row.total {
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
  font-size: 16px;
  font-weight: 600;
}

.summary-price, .total-price {
  color: #ff4757;
  font-weight: 600;
}

.checkout-btn {
  width: 100%;
  padding: 15px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.checkout-btn:hover:not(:disabled) {
  background: #ff3742;
  transform: translateY(-1px);
}

.checkout-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.empty-cart {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-cart-content {
  text-align: center;
  background: white;
  padding: 60px 40px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.empty-cart-icon {
  font-size: 80px;
  margin-bottom: 20px;
}

.empty-cart-content h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.empty-cart-content p {
  color: #666;
  margin-bottom: 30px;
}

.continue-shopping-btn {
  display: inline-block;
  padding: 12px 30px;
  background: #28a745;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.continue-shopping-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .cart-content {
    flex-direction: column;
  }
  
  .cart-summary {
    width: 100%;
  }
  
  .cart-item {
  grid-template-columns: 50px minmax(200px, 3fr) 1fr 1fr 1fr 1fr;

}
.cart-table-header {
    grid-template-columns: 50px minmax(200px, 3fr) 1fr 1fr 1fr 1fr;

    }

  
  .product-title {
    font-size: 14px;
  }
  
  .item-actions {
    flex-direction: row;
  }
}