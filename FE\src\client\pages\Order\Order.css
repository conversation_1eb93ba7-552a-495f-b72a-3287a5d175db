.order-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.order-header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.back-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.back-btn:hover {
  background: #5a6268;
}

.order-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 30px;
}

/* Order Summary */
.order-summary {
  background: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  height: fit-content;
}

.order-summary h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.order-items {
  margin-bottom: 20px;
}

.order-item {
  display: flex;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #e9ecef;
}

.order-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.item-info {
  flex: 1;
}

.item-info h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 14px;
  color: #666;
}

.item-total {
  font-weight: bold;
  color: #e74c3c !important;
}

.order-total {
  text-align: right;
  font-size: 18px;
  color: #e74c3c;
  padding-top: 15px;
  border-top: 2px solid #e9ecef;
}

/* Delivery Information */
.delivery-info {
  background: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.delivery-info h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.delivery-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-group input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.address-input-group {
  display: flex;
  gap: 10px;
}

.address-input-group input {
  flex: 1;
}

.map-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
  transition: background-color 0.3s;
}

.map-btn:hover {
  background: #138496;
}

.location-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.location-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.location-btn:hover {
  background: #218838;
}

.coordinates-info {
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 5px 10px;
  border-radius: 4px;
}

/* Payment Methods */
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.payment-option {
  position: relative;
}

.payment-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.payment-label {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.payment-option input[type="radio"]:checked + .payment-label {
  border-color: #007bff;
  background: #f8f9ff;
  box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.payment-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background: #f8f9fa;
  flex-shrink: 0;
}

.payment-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 4px;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.payment-name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.payment-desc {
  font-size: 14px;
  color: #666;
}

.map-container {
  margin-top: 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.map-placeholder {
  height: 300px;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  text-align: center;
}

.map-placeholder p {
  margin: 5px 0;
}

/* Order Actions */
.order-actions {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.submit-order-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  min-width: 200px;
}

.submit-order-btn:hover:not(:disabled) {
  background: #c0392b;
}

.submit-order-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .order-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .order-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .address-input-group {
    flex-direction: column;
  }
  
  .location-actions {
    justify-content: center;
  }
  
  .container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .order-page {
    padding: 10px 0;
  }
  
  .order-summary,
  .delivery-info {
    padding: 20px;
  }
  
  .submit-order-btn {
    width: 100%;
    padding: 15px;
  }
}
