.dashboard-page {
  padding: 0;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin-top: 30px;
}

/* Status badges */
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-processing {
  background: #d1ecf1;
  color: #0c5460;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

.status-default {
  background: #e9ecef;
  color: #495057;
}

/* Table styles */
.table-responsive {
  overflow-x: auto;
}

.order-id {
  font-weight: 600;
  color: #007bff;
}

.order-total {
  font-weight: 600;
  color: #28a745;
}

/* Top Products */
.top-products-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.product-item:hover {
  background: #e9ecef;
}

.product-rank {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 5px;
}

.product-stats {
  display: flex;
  gap: 15px;
  font-size: 14px;
}

.sales-count {
  color: #6c757d;
}

.revenue-amount {
  color: #28a745;
  font-weight: 500;
}

/* Chart placeholder */
.chart-placeholder {
  height: 300px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 16px;
  border: 2px dashed #dee2e6;
}

/* Quick Actions */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.quick-action-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.quick-action-icon {
  font-size: 32px;
  margin-bottom: 15px;
}

.quick-action-title {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 10px;
}

.quick-action-desc {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 20px;
}

.quick-action-btn {
  width: 100%;
  padding: 10px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.quick-action-btn:hover {
  background: #0056b3;
}

/* Activity Feed */
.activity-feed {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: #e9ecef;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #adb5bd;
}

/* Responsive */
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  .dashboard-content {
    gap: 20px;
  }
  
  .admin-table {
    font-size: 14px;
  }
  
  .admin-table th,
  .admin-table td {
    padding: 10px 8px;
  }
  
  .product-item {
    padding: 12px;
  }
  
  .product-rank {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
  
  .product-name {
    font-size: 14px;
  }
  
  .product-stats {
    font-size: 12px;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .card-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .btn-admin {
    width: 100%;
    justify-content: center;
  }
  
  .table-responsive {
    font-size: 12px;
  }
  
  .admin-table th,
  .admin-table td {
    padding: 8px 4px;
  }
  
  .status-badge {
    font-size: 10px;
    padding: 2px 8px;
  }
}
