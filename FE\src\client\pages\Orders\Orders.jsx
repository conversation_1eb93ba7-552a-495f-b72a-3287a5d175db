import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { OrderService } from '../../services/orderService';
import { toast } from 'react-toastify';
import './Orders.css';

const Orders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await OrderService.getUserOrders();
      
      if (response?.code === 200) {
        // Convert single order to array if needed
        const ordersData = Array.isArray(response.order) ? response.order : [response.order].filter(Boolean);
        setOrders(ordersData);
      } else {
        setOrders([]);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Không thể tải danh sách đơn hàng');
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return '#f39c12';
      case 'confirmed': return '#3498db';
      case 'shipping': return '#9b59b6';
      case 'delivered': return '#27ae60';
      case 'cancelled': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'pending': return 'Chờ xác nhận';
      case 'confirmed': return 'Đã xác nhận';
      case 'shipping': return 'Đang giao hàng';
      case 'delivered': return 'Đã giao hàng';
      case 'cancelled': return 'Đã hủy';
      default: return status;
    }
  };

  const calculateOrderTotal = (products) => {
    return products.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  };

  if (loading) {
    return (
      <div className="orders-page">
        <div className="container">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Đang tải đơn hàng...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="orders-page">
      <div className="container">
        <div className="orders-header">
          <h1>Đơn hàng của tôi</h1>
          <Link to="/" className="continue-shopping-btn">
            Tiếp tục mua sắm
          </Link>
        </div>

        {orders.length > 0 ? (
          <div className="orders-list">
            {orders.map((order) => (
              <div key={order._id} className="order-card">
                <div className="order-header">
                  <div className="order-info">
                    <h3>Đơn hàng #{order._id.slice(-8)}</h3>
                    <p className="order-date">
                      {new Date(order.createdAt).toLocaleDateString('vi-VN')}
                    </p>
                  </div>
                  <div className="order-status">
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(order.status) }}
                    >
                      {getStatusText(order.status)}
                    </span>
                  </div>
                </div>

                <div className="order-content">
                  <div className="order-products">
                    {order.products.map((item, index) => (
                      <div key={index} className="order-product">
                        <div className="product-info">
                          <span className="product-name">
                            Sản phẩm ID: {item.product_id}
                          </span>
                          <span className="product-quantity">
                            x{item.quantity}
                          </span>
                        </div>
                        <div className="product-price">
                          ₫{(item.price * item.quantity).toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="order-details">
                    <div className="delivery-info">
                      <h4>Thông tin giao hàng</h4>
                      <p><strong>Người nhận:</strong> {order.userInfo.fullName}</p>
                      <p><strong>Số điện thoại:</strong> {order.userInfo.phone}</p>
                      <p><strong>Địa chỉ:</strong> {order.userInfo.address}</p>
                    </div>

                    <div className="order-total">
                      <h4>Tổng tiền: ₫{calculateOrderTotal(order.products).toLocaleString()}</h4>
                    </div>
                  </div>
                </div>

                <div className="order-actions">
                  <Link 
                    to={`/orders/${order._id}`} 
                    className="btn-detail"
                  >
                    Xem chi tiết
                  </Link>
                  
                  {order.status === 'shipping' && (
                    <Link 
                      to={`/orders/${order._id}/track`} 
                      className="btn-track"
                    >
                      Theo dõi đơn hàng
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-orders">
            <div className="no-orders-content">
              <div className="no-orders-icon">📦</div>
              <h2>Chưa có đơn hàng nào</h2>
              <p>Bạn chưa có đơn hàng nào. Hãy bắt đầu mua sắm ngay!</p>
              <Link to="/products" className="start-shopping-btn">
                Bắt đầu mua sắm
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Orders;

// CSS will be added in Orders.css file
