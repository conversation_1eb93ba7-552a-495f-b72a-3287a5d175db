import { apiService } from '../../shared/services/api';

export const PaymentService = {
  // Create MoMo payment
  createMoMoPayment: async (paymentData) => {
    try {
      const response = await apiService.post("/checkout/create_payment_urlMomo", {
        amount: paymentData.amount,
        orderInfo: paymentData.orderInfo,
        redirectUrl: paymentData.redirectUrl
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Create VNPay payment (can be used for ZaloPay alternative)
  createVNPayPayment: async (paymentData) => {
    try {
      const response = await apiService.post("/checkout/create_payment_url", {
        total: paymentData.amount,
        orderInfo: paymentData.orderInfo,
        returnUrl: paymentData.returnUrl
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Process payment based on method
  processPayment: async (orderData, paymentMethod, totalAmount) => {
    const baseUrl = window.location.origin;
    
    switch (paymentMethod) {
      case 'momo':
        return await PaymentService.createMoMoPayment({
          amount: totalAmount,
          orderInfo: `Thanh toán đơn hàng ${Date.now()}`,
          redirectUrl: `${baseUrl}/payment-result`
        });
        
      case 'zalopay':
        // For now, use VNPay as ZaloPay alternative
        return await PaymentService.createVNPayPayment({
          amount: totalAmount,
          orderInfo: `Thanh toán đơn hàng ${Date.now()}`,
          returnUrl: baseUrl
        });
        
      case 'cod':
        // Cash on delivery - no payment processing needed
        return { success: true, paymentMethod: 'cod' };
        
      default:
        throw new Error('Phương thức thanh toán không hợp lệ');
    }
  }
};

export default PaymentService;
