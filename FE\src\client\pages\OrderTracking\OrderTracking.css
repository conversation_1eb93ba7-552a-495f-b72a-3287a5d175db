.order-tracking-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.tracking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-info h1 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 28px;
  font-weight: 700;
}

.header-info p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.back-btn {
  background: #6c757d;
  color: white;
  padding: 12px 20px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s;
}

.back-btn:hover {
  background: #5a6268;
}

.tracking-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Status Timeline */
.status-timeline {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.status-timeline h2 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 22px;
  font-weight: 600;
}

.timeline {
  display: flex;
  justify-content: space-between;
  position: relative;
  padding: 20px 0;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: #e9ecef;
  z-index: 1;
}

.timeline-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
}

.step-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 15px;
  transition: all 0.3s;
}

.timeline-step.completed .step-icon {
  background: #28a745;
  color: white;
}

.timeline-step.active .step-icon {
  background: #007bff;
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
  }
}

.step-content {
  text-align: center;
}

.step-content h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.step-time {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.step-connector {
  position: absolute;
  top: 30px;
  left: 50%;
  right: -50%;
  height: 2px;
  background: #28a745;
  z-index: 1;
}

.timeline-step:not(.completed) .step-connector {
  background: #e9ecef;
}

/* Delivery Map */
.delivery-map {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.delivery-map h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 22px;
  font-weight: 600;
}

.map-container {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.map-container .leaflet-container {
  height: 400px !important;
  width: 100% !important;
}

.map-legend {
  display: flex;
  gap: 20px;
  justify-content: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.legend-icon {
  font-size: 16px;
}

.legend-icon.blue {
  color: #007bff;
}

.legend-icon.red {
  color: #dc3545;
}

/* Order Details */
.order-details {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.order-details h2 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 22px;
  font-weight: 600;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.customer-info h3,
.order-summary h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.customer-info p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f1f1f1;
  font-size: 14px;
}

.product-item:last-child {
  border-bottom: none;
}

.total-amount {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 2px solid #e9ecef;
  text-align: right;
  color: #e74c3c;
  font-size: 18px;
}

/* Loading & Error */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 24px;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .tracking-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 20px;
  }
  
  .header-info h1 {
    font-size: 24px;
  }
  
  .status-timeline,
  .delivery-map,
  .order-details {
    padding: 20px;
  }
  
  .timeline {
    flex-direction: column;
    gap: 30px;
  }
  
  .timeline::before {
    top: 0;
    bottom: 0;
    left: 30px;
    right: auto;
    width: 2px;
    height: auto;
  }
  
  .timeline-step {
    flex-direction: row;
    align-items: center;
    text-align: left;
  }
  
  .step-icon {
    margin-bottom: 0;
    margin-right: 20px;
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .step-connector {
    display: none;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .map-legend {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .map-container .leaflet-container {
    height: 300px !important;
  }
  
  .step-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .product-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
